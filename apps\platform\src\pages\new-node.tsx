import Icon from '@ant-design/icons';
import { parse } from 'query-string';
import type { CSSProperties, ReactNode } from 'react';
import { useEffect, useState } from 'react';
import { history, useLocation } from 'umi';

import { ReactComponent as DataSource } from '@/assets/data-source.svg';
import { ReactComponent as Workbench } from '@/assets/icon-home.svg';
import { ReactComponent as LogCenter } from '@/assets/icon-log.svg';
import { ReactComponent as MessageCenter } from '@/assets/icon-message.svg';
import { ReactComponent as DataManager } from '@/assets/jiaochabiao.svg';
import { ReactComponent as CooperativeNode } from '@/assets/join-node.svg';
import { ReactComponent as ResultManager } from '@/assets/resultmanager.svg';
import { CommonLayout } from '@/modules/layout/common-layout';
import { LabelWithBadge } from '@/modules/layout/edge-layout/left-view';
import { HomeLayout } from '@/modules/layout/home-layout';
import { HomeLayoutService } from '@/modules/layout/home-layout/home-layout.service';
import { ManagementLayoutComponent } from '@/modules/layout/management-layout';
import { LoginService } from '@/modules/login/login.service';
import { MessageService } from '@/modules/message-center/message.service';
import { MyNodeService } from '@/modules/my-node/my-node.service';
import { NodeService } from '@/modules/node';
import ResultManagerComponent from '@/modules/result-manager/result-manager.view';
import { useModel } from '@/util/valtio-helper';

type MenuItem = {
  label: ReactNode;
  icon?: ReactNode;
  // component: React.ReactNode;
  key: string;
  children?: MenuItem[];
};

const IconStyle: CSSProperties = {
  width: 20,
  height: 20,
  fontSize: 20,
};

const NodePage = () => {
  const { search } = useLocation();
  const { ownerId } = parse(search);
  const homeLayoutService = useModel(HomeLayoutService);
  const messageService = useModel(MessageService);
  const nodeService = useModel(NodeService);
  const myNodeService = useModel(MyNodeService);
  const loginService = useModel(LoginService);

  const defaultMenuItems: MenuItem[] = [
    {
      label: '首页',
      icon: <Icon style={IconStyle} component={Workbench} />,
      key: 'home',
    },
    {
      label: '数据源管理',
      icon: <Icon component={DataSource} />,
      // component: <DataSourceListComponent />,
      key: 'data-source',
    },
    {
      label: '数据管理',
      icon: <Icon component={DataManager} />,
      // component: <DataManagerComponent />,
      key: 'data-management',
      children: [
        {
          label: '我的数据',
          key: 'data-manage',
        },
        {
          label: '外部数据',
          key: 'data-external',
        },
      ],
    },
    {
      label: '合作节点',
      icon: <Icon component={CooperativeNode} />,
      // component: <CooperativeNodeListComponent />,
      key: 'cooperative',
    },
    {
      label: '结果管理',
      icon: <Icon component={ResultManager} />,
      // component: <ResultManagerComponent />,
      key: 'result',
    },
    {
      label: (
        <LabelWithBadge label="消息中心" count={homeLayoutService.messageCount || 0} />
      ),
      icon: <Icon style={IconStyle} component={MessageCenter} />,
      key: 'messages',
    },
    {
      label: '日志中心',
      icon: <Icon style={IconStyle} component={LogCenter} />,
      key: 'logs',
    },
    {
      label: '账户中心',
      icon: <Icon style={IconStyle} component={LogCenter} />,
      key: 'users',
    },
  ];

  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);

  useEffect(() => {
    const getNodeList = async () => {
      const nodeList = await nodeService.listNode();
      if (ownerId) {
        const node = nodeList.find((n) => ownerId === n.nodeId);
        if (node) nodeService.setCurrentNode(node);
      }
    };
    const getMessageTotal = async () => {
      if (ownerId) {
        const res = await messageService.getMessageCount(ownerId as string);
        if (res.status) {
          homeLayoutService.setMessageCount(res?.data || 0);
        }
      }
    };
    homeLayoutService.setSubTitle('Edge');
    getNodeList();
    // 获取未处理消息数量
    getMessageTotal();
  }, []);

  useEffect(() => {
    const getNodeInfo = async () => {
      await myNodeService.getNodeInfo(ownerId as string);
      let items = [...defaultMenuItems];
      const loginInfo = loginService.userInfo;

      /**
       * 遇到 tee 节点，先屏蔽 数据源管理
       * 原因：tee 暂不支持 oss 数据源管理
       */
      if (
        myNodeService.nodeInfo.nodeId === 'tee' &&
        myNodeService.nodeInfo.type === 'embedded'
      ) {
        items = items.filter((item) => item.key !== 'data-source');
      }

      // 根据权限过滤菜单
      if (!(loginService.userInfo?.apiResources instanceof Array)) {
        items = loginService.getVisibleMenu(items) as MenuItem[];
        console.log(items, 'real items');
      }

      setMenuItems(items);
    };

    getNodeInfo();
  }, [ownerId]);

  return (
    menuItems.length > 0 && (
      <CommonLayout menuItems={menuItems} type="node"></CommonLayout>
    )
    // <HomeLayout>
    //   <ManagementLayoutComponent
    //     menuItems={menuItems}
    //     origin="node"
    //     defaultTabKey={defaultTabKey}
    //   />
    // </HomeLayout>
  );
};

export default NodePage;
