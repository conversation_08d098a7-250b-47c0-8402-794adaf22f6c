/* eslint-disable */
import request from 'umi-request';

/** 获取中心账户列表 获取中心模式下的账户列表 POST /api/v1alpha1/user/centerAccountList */
export async function getCenterAccountList(options?: { [key: string]: any }) {
  return request<API2.SecretPadResponseListUserListVO>(
    '/api/v1alpha1/user/centerAccountList',
    {
      method: 'POST',
      ...(options || {}),
    },
  );
}

/** 创建用户 根据部署模式和角色权限创建新用户 POST /api/v1alpha1/user/create */
export async function createUser2(
  body: API2.UserCreateRequest,
  options?: { [key: string]: any },
) {
  return request<API2.SecretPadResponseBoolean>('/api/v1alpha1/user/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 删除用户 根据用户名删除用户 POST /api/v1alpha1/user/delete */
export async function deleteUsingPost(body: string, options?: { [key: string]: any }) {
  return request<API2.SecretPadResponseBoolean>('/api/v1alpha1/user/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 获取用户信息 查询当前登录用户的基本信息 POST /api/v1alpha1/user/get */
export async function get(options?: { [key: string]: any }) {
  return request<API2.SecretPadResponseUserContextDTO>('/api/v1alpha1/user/get', {
    method: 'POST',
    ...(options || {}),
  });
}

/** 获取机构开发者列表 获取机构开发者用户列表 POST /api/v1alpha1/user/institutionDeveloperList */
export async function getInstitutionDeveloperList(options?: { [key: string]: any }) {
  return request<API2.SecretPadResponseListUserListVO>(
    '/api/v1alpha1/user/institutionDeveloperList',
    {
      method: 'POST',
      ...(options || {}),
    },
  );
}

/** 获取节点用户列表 获取节点用户列表 POST /api/v1alpha1/user/nodeUserList */
export async function getNodeUserList(options?: { [key: string]: any }) {
  return request<API2.SecretPadResponseListUserListVO>(
    '/api/v1alpha1/user/nodeUserList',
    {
      method: 'POST',
      ...(options || {}),
    },
  );
}

/** 修改用户密码 用户修改自己的登录密码 POST /api/v1alpha1/user/updatePwd */
export async function updatePwd(
  body: API2.UserUpdatePwdRequest,
  options?: { [key: string]: any },
) {
  return request<API2.SecretPadResponseBoolean>('/api/v1alpha1/user/updatePwd', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
