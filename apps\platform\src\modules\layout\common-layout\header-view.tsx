import { LogoutOutlined, DownOutlined } from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { Avatar, Button, Divider, Dropdown, Empty, Popover, Space, Spin } from 'antd';
import { parse } from 'query-string';
import { useEffect, useState } from 'react';
import { history, useLocation } from 'umi';

import centerOfflineImgLink from '@/assets/center-offline.png';
import centerImgLink from '@/assets/center.png';
import edgeOfflineImgLink from '@/assets/edge-offline.png';
import edgeImgLink from '@/assets/edge.png';
import { ReactComponent as IconAvatar } from '@/assets/icon-avatar.svg';
import { ReactComponent as IconBack } from '@/assets/icon-back.svg';
import { hasAccess, Platform } from '@/components/platform-wrapper';
import { GuideTourService } from '@/modules/guide-tour/guide-tour-service';
import { ChangePasswordModal } from '@/modules/login/component/change-password';
import { LoginService } from '@/modules/login/login.service';
import { logout } from '@/services/secretpad/AuthController';
import { get as getInst } from '@/services/secretpad/InstController';
import { get } from '@/services/secretpad/NodeController';
import { getImgLink } from '@/util/tracert-helper';
import { getModel, Model, useModel } from '@/util/valtio-helper';

import { CenterLayoutService, routeLabelMap } from './center-layout.service';
import styles from './index.less';
import { VersionService } from './version-service';

export const HeaderComponent = () => {
  const viewInstance = useModel(HeaderModel);
  const layoutService = useModel(CenterLayoutService);
  const loginService = useModel(LoginService);
  const versionService = useModel(VersionService);

  const { search, pathname, state } = useLocation();
  const { ownerId } = parse(search);

  const onLogout = async () => {
    await logout({});
    history.push('/login');
  };

  const content = (
    <Spin spinning={versionService.loading}>
      <div className={styles.headerDropdown}>
        {versionService.versionList.length === 0 ? (
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        ) : (
          <Space direction="vertical">
            {versionService.versionList.map((item) => {
              return <div key={item.name}>{`${item.name}版本：${item.version}`}</div>;
            })}
          </Space>
        )}
      </div>
    </Spin>
  );

  const handleOpenChange = async (open: boolean) => {
    if (open) {
      await versionService.getVersion();
    }
  };

  const items = [
    // {
    //   key: 'version',
    //   label: (
    //     <Popover
    //       content={content}
    //       trigger="hover"
    //       placement="left"
    //       onOpenChange={handleOpenChange}
    //     >
    //       组件版本
    //     </Popover>
    //   ),
    // },
    // {
    //   key: 'inst',
    //   label: <div>我的机构</div>,
    // },
    {
      key: 'changePassword',
      label: <div>修改密码</div>,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: <div>退出</div>,
    },
  ];

  const onMenuClick: MenuProps['onClick'] = ({ key }) => {
    if (key === 'logout') {
      onLogout();
    } else if (key === 'changePassword') {
      viewInstance.showChangePassword();
    } else if (key === 'inst') {
      history.push(
        {
          pathname: '/edge/my-node',
          search: `ownerId=${ownerId}`,
        },
        {
          ...(state as { [key: string]: string }),
          origin: window.location.pathname + window.location.search,
        },
      );
    }
  };

  const isAutonomyMode = hasAccess({ type: [Platform.AUTONOMY] });

  const platFormModeItems = items;

  const goBack = () => {
    console.log(state, 'go back');
    if ((state as { origin: string })?.origin) {
      const newState = { ...(state as { [key: string]: string }) };
      delete newState.origin;
      history.push((state as { origin: string }).origin, newState);
    } else {
      history.go(-1);
    }
  };

  useEffect(() => {
    const getNodeName = async (ownerId: string) => {
      if (!ownerId) return;
      if (isAutonomyMode) {
        const info = await getInst({
          instId: ownerId,
        });
        viewInstance.instName = info.data?.instName || '';
      } else {
        const info = await get({
          nodeId: ownerId,
        });
        viewInstance.nodeName = info.data?.nodeName || '';
      }
    };
    if (viewInstance.showMyNode(pathname)) {
      getNodeName(ownerId as string);
    }
  }, []);

  useEffect(() => {
    if (!loginService?.userInfo) return;

    // 获取平台类型
    // const platformType = loginService.userInfo.platformType;

    // if (platformType) {
    //   // 如果是 CENTER / EDGE
    //   const avatarInfo = avatarMapping[platformType];
    //   setAvatarOfflineLink(avatarInfo.offlineLink);

    //   const imgLink = getImgLink(avatarInfo);
    //   setAvatarLink(imgLink);
    // }
  }, [loginService?.userInfo]);

  return (
    <div className={styles['header-items']}>
      <div className={styles.left}>
        {layoutService.routeKeys.length > 1 && (
          <>
            <Space style={{ cursor: 'pointer' }} onClick={goBack}>
              <IconBack />
              <span className={styles.backText}>返回</span>
            </Space>
            <Divider type="vertical" />
          </>
        )}

        {layoutService.routeKeys.map((key, index) => {
          return (
            <div
              className={
                index === layoutService.routeKeys.length - 1
                  ? styles.currentRoute
                  : styles.route
              }
              key={key}
            >
              {routeLabelMap[key as keyof typeof routeLabelMap]}
              {index < layoutService.routeKeys.length - 1 && '/'}
            </div>
          );
        })}
      </div>
      <div className={styles.right}>
        {/* {layoutService.showBackButton && (
          <>
            <span
              className={styles.community}
              onClick={() => history.push('/home?tab=project-management')}
            >
              <SwapOutlined />
              返回工作台
            </span>
            <span className={styles.line} />
          </>
        )} */}
        {ownerId && (
          <Button
            size="small"
            color="primary"
            variant="filled"
            className={styles.myNodeTitle}
            onClick={() =>
              history.push({
                pathname: '/node/my-node',
                search: `ownerId=${ownerId}`,
              })
            }
          >
            <span className={styles.nodeName}>
              {isAutonomyMode ? viewInstance.instName : viewInstance.nodeName}
            </span>
            {isAutonomyMode ? '机构' : '节点'}
          </Button>
        )}
        <Dropdown
          menu={{
            items: platFormModeItems,
            onClick: onMenuClick,
          }}
        >
          <div style={{ cursor: 'pointer' }} onClick={(e) => e.preventDefault()}>
            <Space>
              <Avatar
                size={20}
                style={{ border: 'none' }}
                // 用 icon 代替 Image 的 fallback
                // Antd: Avatar 组件中，可以设置 icon 或 children 作为图片加载失败的默认 fallback 行为.
                icon={<IconAvatar />}
              />
              {loginService?.userInfo?.name}
              <DownOutlined />
            </Space>
          </div>
        </Dropdown>
        <ChangePasswordModal
          visible={viewInstance.showChangePasswordModel}
          close={() => (viewInstance.showChangePasswordModel = false)}
        />
      </div>
    </div>
  );
};

export class HeaderModel extends Model {
  edgeLayoutService = getModel(CenterLayoutService);
  guideTourService = getModel(GuideTourService);

  nodeName = '';

  instName = '';

  showChangePasswordModel = false;

  goto(url: string) {
    const a = document.createElement('a');
    a.href = url;
    a.target = '_blank';
    a.click();
  }

  showGoToHome = (path: string) => {
    return path.startsWith('/node');
  };

  showMyNode = (path: string) => {
    // const pathnameToShowNode = ['/node', '/message', '/edge'];
    return path.includes('edge') || path.includes('/node');
  };

  showMessage = (path: string) => {
    const pathnameToShowNode = ['/node', '/message']; // 暂时将edge路由去掉，使用侧边栏的消息中心
    return pathnameToShowNode.indexOf(path) > -1;
  };

  showGuide = (path: string) => {
    const pathnameToShowGuide = ['/', '/home'];
    return pathnameToShowGuide.indexOf(path) > -1;
  };

  showChangePassword = () => {
    this.showChangePasswordModel = true;
  };

  reExperience = () => {
    this.guideTourService.reset();
    history.push({
      pathname: '/guide',
    });
  };
}
