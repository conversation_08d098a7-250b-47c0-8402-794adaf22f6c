import { ExclamationCircleFilled, SearchOutlined } from '@ant-design/icons';
import { Tag, Button, Input, Flex, Space, Modal } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { parse } from 'query-string';
import { useEffect, useState } from 'react';

import CommonTable from '@/components/common-table';
import { StatusTag } from '@/components/status-tag';
import { TabSearchBox } from '@/components/tab-search-box';
import { getModel, Model, useModel } from '@/util/valtio-helper';

import { LoginService } from '../login/login.service';

import AddUserComponent from './components/add-user';
import { UpdateModal } from './components/update-pwd';
import { UpdatePasswordFormSchema, UpdateUserFormConfig } from './constant';
import styles from './index.less';
import { UserService } from './user.service';

const roleLabelMap = {
  DATA_ADMIN: '数据管理员',
  MODELING_ENGINEER: '开发者',
  INSTITUTION_ADMIN: '机构管理员',
};

const columns: ColumnsType<API2.UserListVO & { index: number }> = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
    width: 60,
  },
  {
    title: '姓名',
    dataIndex: 'fullName',
    key: 'fullName',
  },
  {
    title: '用户名',
    dataIndex: 'userName',
    key: 'userName',
  },
  {
    title: '角色',
    dataIndex: 'roleCode',
    key: 'roleCode',
    render(role: string) {
      return <div>{roleLabelMap[role as keyof typeof roleLabelMap]}</div>;
    },
  },
  {
    title: '添加时间',
    dataIndex: 'createTime',
    key: 'createTime',
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    render: (status: boolean) => (
      <StatusTag type={status ? 'success' : 'failed'} text={status ? '可用' : '禁用'} />
    ),
  },
];

const InputConfirmModal = ({
  open,
  account,
  onOk,
  onCancel,
}: {
  open: boolean;
  account: string;
  onOk: () => void;
  onCancel: () => void;
}) => {
  const [value, setValue] = useState('');

  return (
    <Modal
      open={open}
      title={`确认要删除[${account}]吗？`}
      footer={
        <>
          <Button onClick={onCancel}>取消</Button>
          <Button
            type="primary"
            onClick={() => {
              setValue('');
              onOk();
            }}
            disabled={value !== account}
          >
            确定
          </Button>
        </>
      }
    >
      <p>请输入{`[${account}]`}确认操作</p>
      <Input
        value={value}
        onChange={(e) => {
          setValue(e.target.value);
        }}
        placeholder="请输入"
      />
    </Modal>
  );
};

const UserCenterComponent = () => {
  const [visible, setVisible] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [updatePwdOpen, setUpdatePwdOpen] = useState(false);
  const [editOpen, setEditOpen] = useState(false);
  const [showBtn, setShowBtn] = useState(true);
  const service = useModel(UserManagement);
  const loginService = useModel(LoginService);
  const { ownerId } = parse(window.location.search);

  const forbidAccount = (info: API2.UserListVO) => {
    Modal.confirm({
      title: `确定要停用[${info.userName}]吗？`,
      icon: <ExclamationCircleFilled />,
      content: '停用后该用户将无法登录',
      okText: '确定',
      cancelText: '取消',
      okButtonProps: {
        danger: true,
        type: 'default',
      },
      onOk() {
        console.log(info);
      },
    });
  };

  const actionColumn = {
    title: '操作',
    key: 'action',
    width: showBtn ? 250 : 100,
    render: (_: any, record: API2.UserListVO) => (
      <div>
        {showBtn && (
          <Button
            variant="text"
            color="primary"
            size="small"
            disabled={
              record.roleCode === 'INSTITUTION_ADMIN' || record.roleCode === 'ADMIN'
            }
            onClick={() => {
              service.currentUser = record;
              setEditOpen(true);
            }}
          >
            编辑
          </Button>
        )}
        {showBtn && (
          <Button
            variant="text"
            color="primary"
            size="small"
            disabled={
              record.roleCode === 'INSTITUTION_ADMIN' || record.roleCode === 'ADMIN'
            }
            onClick={() => {
              service.currentUser = record;
              setUpdatePwdOpen(true);
            }}
          >
            修改密码
          </Button>
        )}
        <Button
          variant="text"
          color="primary"
          size="small"
          disabled={
            record.roleCode === 'INSTITUTION_ADMIN' || record.roleCode === 'ADMIN'
          }
          onClick={() => {
            service.currentUser = record;
            forbidAccount(record);
          }}
        >
          停用
        </Button>
        {showBtn && (
          <Button
            variant="text"
            color="danger"
            size="small"
            disabled={
              record.roleCode === 'INSTITUTION_ADMIN' || record.roleCode === 'ADMIN'
            }
            onClick={() => {
              service.currentUser = record;
              setDeleteModalOpen(true);
            }}
          >
            删除
          </Button>
        )}
      </div>
    ),
  };

  const tableColumns = [...columns, actionColumn];

  useEffect(() => {
    service.getUserList(loginService.userInfo?.ownerType as 'EDGE' | 'CENTER');
  }, []);
  return (
    <div className={styles.userCenterWrapper}>
      {loginService.userInfo?.ownerType === 'CENTER' ? (
        <TabSearchBox
          tabs={[
            { label: '中心账户', value: 'center' },
            { label: '机构开发者', value: 'developer' },
          ]}
          onTabChange={(val) => {
            service.getUserList(
              loginService.userInfo?.ownerType as 'EDGE' | 'CENTER',
              val,
            );
            if (val === 'developer') {
              setShowBtn(false);
            } else {
              setShowBtn(true);
            }
          }}
        >
          <Space>
            <span>用户名称</span>
            <Input
              placeholder="请输入关键字搜索"
              style={{ width: 200 }}
              suffix={<SearchOutlined style={{ color: '#aaa' }} />}
            />
          </Space>
        </TabSearchBox>
      ) : (
        <div className={styles.userCenterHeader}>
          <Input
            placeholder="请输入关键字搜索"
            style={{ width: 200 }}
            suffix={<SearchOutlined style={{ color: '#aaa' }} />}
          />
        </div>
      )}

      <div className={styles.userCenterContent}>
        <Flex justify="end" style={{ marginBottom: 16 }}>
          <Button type="primary" onClick={() => setVisible(true)}>
            新增用户
          </Button>
        </Flex>
        <CommonTable
          size="middle"
          rowKey="userName"
          columns={tableColumns as ColumnsType}
          dataSource={service.userList}
        />
      </div>
      <AddUserComponent
        open={visible}
        onClose={() => setVisible(false)}
        onSubmit={(value) => {
          service
            .createUser(value, loginService.userInfo!.ownerType, ownerId as string)
            .finally(() => setVisible(false));
        }}
      />
      <InputConfirmModal
        open={deleteModalOpen}
        account={service.currentUser?.userName || ''}
        onCancel={() => setDeleteModalOpen(false)}
        onOk={() => {
          service
            .deleteUser(
              service.currentUser!.userName!,
              loginService.userInfo?.ownerType as 'EDGE' | 'CENTER' | 'P2P',
            )
            .finally(() => setDeleteModalOpen(false));
        }}
      ></InputConfirmModal>
      <UpdateModal
        open={updatePwdOpen}
        title="修改密码"
        schema={UpdatePasswordFormSchema}
        data={service.currentUser}
        onOK={(value) => {
          service.updatePassword(value);
        }}
        onCancel={() => setUpdatePwdOpen(false)}
      ></UpdateModal>
      <UpdateModal
        open={editOpen}
        title="编辑信息"
        schema={UpdateUserFormConfig}
        data={service.currentUser}
        onOK={(value) => {
          service.updateUser(value);
        }}
        onCancel={() => setEditOpen(false)}
      ></UpdateModal>
    </div>
  );
};

export class UserManagement extends Model {
  currentUser: API2.UserListVO | undefined;
  userList: API2.UserListVO[] = [];
  userService = getModel(UserService);
  btnLoading = false;

  async getUserList(ownerType: 'EDGE' | 'CENTER' | 'P2P', type?: string) {
    if (ownerType === 'EDGE') {
      this.userList = (await this.userService.getNodeUsers()) || [];
    } else if (ownerType === 'CENTER') {
      if (type === 'developer') {
        this.userList = (await this.userService.getDeveloperList()) || [];
      } else {
        this.userList = (await this.userService.getCenterUsers()) || [];
      }
    }
    this.userList = this.userList.map((item, index) => ({
      ...item,
      index: index + 1,
    }));
  }

  async createUser(
    data: Record<string, any>,
    ownerType: 'EDGE' | 'CENTER' | 'P2P',
    ownerId?: string,
  ) {
    this.btnLoading = true;
    try {
      await this.userService.createUser(data, ownerType, ownerId);
      this.getUserList(ownerType);
    } finally {
      this.btnLoading = false;
    }
  }

  async updatePassword(data: any) {
    console.log('update password', data);
  }

  async updateUser(data: any) {
    console.log('update user', data);
  }

  async deleteUser(username: string, ownerType: 'EDGE' | 'CENTER' | 'P2P') {
    await this.userService.deleteUser(username);
    this.getUserList(ownerType);
  }
}

export default UserCenterComponent;
