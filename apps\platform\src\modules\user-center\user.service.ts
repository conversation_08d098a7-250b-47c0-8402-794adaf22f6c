import { message } from 'antd';
import sha256 from 'crypto-js/sha256';

import {
  getNodeUserList,
  getCenterAccountList,
  createUser2,
  deleteUsingPost,
  getInstitutionDeveloperList,
} from '@/services/secretpad2/UserController';
import { Model } from '@/util/valtio-helper';

export class UserService extends Model {
  async getNodeUsers() {
    const { data } = await getNodeUserList();
    return data;
  }

  async createUser(
    data: Record<string, any>,
    ownerType: 'EDGE' | 'CENTER' | 'P2P',
    ownerId?: string,
  ) {
    const res = await createUser2({
      name: data.userName,
      fullName: data.fullName,
      passwordHash: sha256(data.password).toString(),
      ownerId,
      ownerType,
      description: data.description,
      roleCode: data.roleCode,
      status: true,
    });
    if (res.status?.code === 0) {
      message.success('创建成功');
    } else {
      message.error('创建失败');
    }
  }

  async getCenterUsers() {
    const { data } = await getCenterAccountList();
    return data;
  }

  async deleteUser(userName: string) {
    const { data } = await deleteUsingPost(userName);
    if (data) {
      message.success('删除成功');
    } else {
      message.error('删除失败');
    }
  }

  async getDeveloperList() {
    const { data } = await getInstitutionDeveloperList();
    return data;
  }
}
